# 路径别名配置说明

本项目已配置@符号路径缩写，可以使用简洁的路径别名来导入文件。

## 配置的路径别名

| 别名 | 指向路径 | 说明 |
|------|----------|------|
| `@` | `src/` | 指向src根目录 |
| `@components` | `src/components/` | 指向组件目录 |
| `@assets` | `src/assets/` | 指向资源目录 |
| `@pages` | `src/pages/` | 指向页面目录 |

## 使用示例

### 在 TypeScript/JavaScript 文件中

```typescript
// 使用 @ 导入根目录文件
import App from '@/App.vue'

// 使用 @components 导入组件
import HelloWorld from '@components/HelloWorld.vue'

// 使用 @assets 导入资源
import logo from '@assets/vue.svg'

// 使用 @pages 导入页面
import HomePage from '@pages/Home.vue'
```

### 在 Vue 模板中

```vue
<template>
  <!-- 在模板中使用@assets -->
  <img src="@assets/vue.svg" alt="Vue logo" />
</template>
```

### 在 CSS 中

```css
.background {
  background-image: url('@assets/background.jpg');
}
```

## 配置文件

### vite.config.ts
配置了Vite的路径别名解析：

```typescript
resolve: {
  alias: {
    '@': resolve(__dirname, 'src'),
    '@components': resolve(__dirname, 'src/components'),
    '@assets': resolve(__dirname, 'src/assets'),
    '@pages': resolve(__dirname, 'src/pages'),
  }
}
```

### tsconfig.app.json
配置了TypeScript的路径映射：

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@assets/*": ["src/assets/*"],
      "@pages/*": ["src/pages/*"]
    }
  }
}
```

## 优势

1. **简洁性**: 避免使用相对路径如 `../../../components/`
2. **可维护性**: 文件移动时不需要更新导入路径
3. **可读性**: 路径含义更加清晰
4. **IDE支持**: 支持自动补全和跳转

## 注意事项

- 确保路径别名在所有配置文件中保持一致
- 新增目录时记得更新路径别名配置
- 在团队开发中确保所有成员了解路径别名规则
