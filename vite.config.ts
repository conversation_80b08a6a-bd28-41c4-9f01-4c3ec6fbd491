/// <reference types="vite/client" />
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@pages': resolve(__dirname, 'src/pages'),
    },
  },
  build: {
    rollupOptions: {
      input: Object.keys(
        import.meta.glob('/src/pages/*/', { eager: true })
      ).reduce<Record<string, string>>((acc, path) => {
        const folderName = path.split('/').slice(-2, -1)[0]; // 提取文件夹名
        acc[folderName] = resolve(__dirname, 'index.html?page=' + folderName);
        return acc;
      }, {}),
    },
    lib: {
      entry: resolve(__dirname, 'src/content-scripts/www.baidu.com/index.ts'),
      name: 'www.baidu.com',
      formats: ['iife'],
      fileName: () => 'content-scripts/www.baidu.com/index.js',
    },
  },
});
